"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var DEFAULT_FIELD_DELIMITER = ',';
var VALID_FIELD_DELIMITERS = [DEFAULT_FIELD_DELIMITER, ';'];
var FieldStringifier = /** @class */ (function () {
    function FieldStringifier(fieldDelimiter) {
        this.fieldDelimiter = fieldDelimiter;
    }
    FieldStringifier.prototype.isEmpty = function (value) {
        return typeof value === 'undefined' || value === null || value === '';
    };
    FieldStringifier.prototype.quoteField = function (field) {
        return "\"" + field.replace(/"/g, '""') + "\"";
    };
    return FieldStringifier;
}());
exports.FieldStringifier = FieldStringifier;
var DefaultFieldStringifier = /** @class */ (function (_super) {
    __extends(DefaultFieldStringifier, _super);
    function DefaultFieldStringifier() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    DefaultFieldStringifier.prototype.stringify = function (value) {
        if (this.isEmpty(value))
            return '';
        var str = String(value);
        return this.needsQuote(str) ? this.quoteField(str) : str;
    };
    DefaultFieldStringifier.prototype.needsQuote = function (str) {
        return str.includes(this.fieldDelimiter) || str.includes('\n') || str.includes('"');
    };
    return DefaultFieldStringifier;
}(FieldStringifier));
var ForceQuoteFieldStringifier = /** @class */ (function (_super) {
    __extends(ForceQuoteFieldStringifier, _super);
    function ForceQuoteFieldStringifier() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ForceQuoteFieldStringifier.prototype.stringify = function (value) {
        return this.isEmpty(value) ? '' : this.quoteField(String(value));
    };
    return ForceQuoteFieldStringifier;
}(FieldStringifier));
function createFieldStringifier(fieldDelimiter, alwaysQuote) {
    if (fieldDelimiter === void 0) { fieldDelimiter = DEFAULT_FIELD_DELIMITER; }
    if (alwaysQuote === void 0) { alwaysQuote = false; }
    _validateFieldDelimiter(fieldDelimiter);
    return alwaysQuote ? new ForceQuoteFieldStringifier(fieldDelimiter) : new DefaultFieldStringifier(fieldDelimiter);
}
exports.createFieldStringifier = createFieldStringifier;
function _validateFieldDelimiter(delimiter) {
    if (VALID_FIELD_DELIMITERS.indexOf(delimiter) === -1) {
        throw new Error("Invalid field delimiter `" + delimiter + "` is specified");
    }
}
//# sourceMappingURL=field-stringifier.js.map