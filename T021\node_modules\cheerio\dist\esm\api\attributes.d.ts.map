{"version": 3, "file": "attributes.d.ts", "sourceRoot": "", "sources": ["../../../src/api/attributes.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAS,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE,MAAM,YAAY,CAAC;AAC/D,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AA6F7C;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,GACX,MAAM,GAAG,SAAS,CAAC;AACtB;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GACf,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;AACtC;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,EACZ,KAAK,CAAC,EACF,MAAM,GACN,IAAI,GACJ,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,CAAC,GAChE,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,GACpC,OAAO,CAAC,CAAC,CAAC,CAAC;AAyFd,UAAU,SAAS;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;IAC/B,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;CACzB;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,SAAS,GAAG,UAAU,GAC3B,MAAM,GAAG,SAAS,CAAC;AACtB,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,aAAa,GAC5D,MAAM,GAAG,IAAI,CAAC;AACjB;;;;;;GAMG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,OAAO,GACZ,SAAS,GAAG,SAAS,CAAC;AACzB;;;;;;;;;;;;;GAaG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,GAAG,KAAK,GACnB,MAAM,GAAG,SAAS,CAAC;AACtB;;;;;GAKG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,MAAM,OAAO,EAC7D,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,CAAC,GACN,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;GAMG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,MAAM,OAAO,EAC7D,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,CAAC,EACP,KAAK,EACD,OAAO,CAAC,CAAC,CAAC,GACV,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,GAClE,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,GAC7D,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;GAMG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,EACZ,KAAK,EACD,MAAM,GACN,OAAO,GACP,IAAI,GACJ,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,OAAO,CAAC,GACjE,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;GAKG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;AA8NhF;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,GACX,OAAO,CAAC;AACX;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GACf,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3B;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,OAAO,GACb,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAC9B,OAAO,CAAC,CAAC,CAAC,CAAC;AAgCd;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,GAAG,CAAC,CAAC,SAAS,OAAO,EACnC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GACf,MAAM,GAAG,SAAS,GAAG,MAAM,EAAE,CAAC;AACjC;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,GAAG,CAAC,CAAC,SAAS,OAAO,EACnC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GACvB,OAAO,CAAC,CAAC,CAAC,CAAC;AAsEd;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,OAAO,EAC1C,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,CAAC,CAAC,CAUZ;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,OAAO,EACxC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,SAAS,EAAE,MAAM,GAChB,OAAO,CAoBT;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,SAAS,CAAC,CAAC,CAAC,EAChE,IAAI,EAAE,CAAC,EACP,KAAK,CAAC,EACF,MAAM,GACN,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,KAAK,MAAM,GAAG,SAAS,CAAC,GACxE,CAAC,CAyCH;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,SAAS,CAAC,CAAC,CAAC,EACnE,IAAI,EAAE,CAAC,EACP,IAAI,CAAC,EACD,MAAM,GACN,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,KAAK,MAAM,GAAG,SAAS,CAAC,GACxE,CAAC,CA2CH;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,SAAS,CAAC,CAAC,CAAC,EACnE,IAAI,EAAE,CAAC,EACP,KAAK,CAAC,EACF,MAAM,GACN,CAAC,CACC,IAAI,EAAE,OAAO,EACb,CAAC,EAAE,MAAM,EACT,SAAS,EAAE,MAAM,EACjB,QAAQ,CAAC,EAAE,OAAO,KACf,MAAM,CAAC,EAChB,QAAQ,CAAC,EAAE,OAAO,GACjB,CAAC,CA+CH"}