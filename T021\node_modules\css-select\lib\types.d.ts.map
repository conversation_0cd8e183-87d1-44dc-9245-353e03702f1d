{"version": 3, "file": "types.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAEzC,oBAAY,gBAAgB,GAAG,QAAQ,GAAG;IAAE,IAAI,EAAE,qBAAqB,CAAA;CAAE,CAAC;AAE1E,oBAAY,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,CAAC;AACrD,MAAM,WAAW,OAAO,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI;IACnD;;OAEG;IACH,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,WAAW,CAAC;IAE3C;;OAEG;IACH,SAAS,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,OAAO,CAAC;IAEpE;;OAEG;IACH,iBAAiB,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,SAAS,CAAC;IAE3E;;OAEG;IACH,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC;IAEpC;;OAEG;IACH,OAAO,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,MAAM,CAAC;IAEvC;;OAEG;IACH,SAAS,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,GAAG,IAAI,CAAC;IAE9C;;;OAGG;IACH,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC;IAEpC;;OAEG;IACH,kBAAkB,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,WAAW,GAAG,IAAI,CAAC;IAExD;;OAEG;IACH,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,MAAM,CAAC;IAEhC;;OAEG;IACH,SAAS,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC;IAExD;;;OAGG;IACH,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;IAEzC;;;OAGG;IACH,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,WAAW,EAAE,CAAC;IAExE;;;OAGG;IACH,OAAO,EAAE,CACL,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,EAC5B,KAAK,EAAE,IAAI,EAAE,KACZ,WAAW,GAAG,IAAI,CAAC;IAExB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,KAAK,OAAO,CAAC;IAEvC;;OAEG;IACH,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,OAAO,CAAC;IAE3C;;OAEG;IACH,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,OAAO,CAAC;IAE3C;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,OAAO,CAAC;CAC7C;AAED,MAAM,WAAW,OAAO,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI;IACnD;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAClC;;;;OAIG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB;;;;;;OAMG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;;;;;OAOG;IACH,OAAO,CAAC,EACF,MAAM,CACF,MAAM,EACN,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,KAAK,OAAO,CAAC,CACnE,GACD,SAAS,CAAC;IAChB;;;OAGG;IACH,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,KAAK,OAAO,CAAC;IAC7C;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACrC;;;OAGG;IACH,OAAO,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;IACxB;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;;;OAMG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;CAC1B;AAGD,MAAM,WAAW,eAAe,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,CAC3D,SAAQ,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC;IAClC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACpC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,KAAK,OAAO,CAAC;CACzC;AAED,MAAM,WAAW,aAAa,CAAC,WAAW;IACtC,CAAC,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC;IAC7B,sBAAsB,CAAC,EAAE,OAAO,CAAC;CACpC;AACD,oBAAY,KAAK,CAAC,WAAW,IACvB,MAAM,GACN,aAAa,CAAC,WAAW,CAAC,GAC1B,QAAQ,EAAE,EAAE,CAAC;AACnB,oBAAY,YAAY,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,IAAI,CACvD,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAC3B,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAC3C,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,KACtB,aAAa,CAAC,WAAW,CAAC,CAAC"}