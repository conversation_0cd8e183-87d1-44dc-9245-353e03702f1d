{"name": "csv-writer", "version": "1.6.0", "description": "Convert objects/arrays into a CSV string or write them into a CSV file", "main": "dist/index.js", "types": "src/index.ts", "scripts": {"compile": "tsc -p ./", "test": "npm run test:unit && npm run test:it", "pretest:unit": "rm -rf test-tmp && mkdir test-tmp", "test:unit": "mocha --require ts-node/register --recursive 'src/test/**/*.ts'", "test:it": "test-integration/test.sh", "coverage": "nyc npm run test:unit", "lint": "tslint -p .", "prepare": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/ryu1kn/csv-writer.git"}, "keywords": ["csv", "writer", "stringify"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ryu1kn/csv-writer/issues"}, "homepage": "https://github.com/ryu1kn/csv-writer#readme", "devDependencies": {"@types/mocha": "^5.2.7", "@types/node": "^12.12.25", "codeclimate-test-reporter": "^0.5.1", "coveralls": "^3.0.9", "mocha": "^7.0.0", "nyc": "^15.0.0", "ts-node": "^8.6.2", "tslint": "^5.20.1", "typescript": "^3.7.5"}}