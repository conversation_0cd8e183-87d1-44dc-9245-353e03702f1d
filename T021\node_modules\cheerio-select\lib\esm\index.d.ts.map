{"version": 3, "file": "index.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio-select/ef063a6ca4c3f0d02d2fc3505e750b6fb81c448d/src/", "sources": ["index.ts"], "names": [], "mappings": "AACA,OAAO,EAEH,KAAK,OAAO,IAAI,gBAAgB,EAEnC,MAAM,YAAY,CAAC;AAGpB,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAU7D,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAYvD,MAAM,WAAW,OAAQ,SAAQ,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;IAC/D,qGAAqG;IACrG,IAAI,CAAC,EAAE,QAAQ,CAAC;CACnB;AAED,wBAAgB,EAAE,CACd,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,OAAO,CAAC,EAC7C,OAAO,GAAE,OAAY,GACtB,OAAO,CAET;AAED,wBAAgB,IAAI,CAChB,QAAQ,EAAE,OAAO,EAAE,EACnB,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,OAAO,CAAC,EAC7C,OAAO,GAAE,OAAY,GACtB,OAAO,CAWT;AAsCD,wBAAgB,MAAM,CAClB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,OAAO,EAAE,EACnB,OAAO,GAAE,OAAY,GACtB,OAAO,EAAE,CAEX;AAoGD,wBAAgB,MAAM,CAClB,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,OAAO,CAAC,EAC7C,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE,EACzB,OAAO,GAAE,OAAY,EACrB,KAAK,SAAW,GACjB,OAAO,EAAE,CA2BX"}