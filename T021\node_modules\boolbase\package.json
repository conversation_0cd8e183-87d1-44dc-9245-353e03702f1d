{"name": "boolbase", "version": "1.0.0", "description": "two functions: One that returns true, one that returns false", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/fb55/boolbase"}, "keywords": ["boolean", "function"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/fb55/boolbase/issues"}, "homepage": "https://github.com/fb55/boolbase"}